# Chat Enhancement Features

本文档描述了聊天系统的增强功能，包括系统提示支持、消息预处理和响应后处理功能。

## 新增功能

### 1. System Prompt 支持

系统提示现在存储在单独的配置文件中，支持多种预定义的提示类型。

#### 配置文件位置
- `src/config/prompts.ts` - 系统提示配置

#### 可用的系统提示类型
- `default` - 通用助手提示
- `creative` - 创意写作助手
- `technical` - 技术专家助手
- `analytical` - 分析型助手

#### 使用方法
```typescript
import { useChat } from '@/hooks/useChat'

// 使用技术助手提示
const chat = useChat({
  systemPromptKey: 'technical'
})
```

### 2. 消息预处理功能

用户输入在发送给 LLM 之前会经过预处理函数处理。

#### 预处理器位置
- `src/utils/messageProcessors.ts` - 消息处理器

#### 内置预处理器
- `default` - 原样返回（默认）
- `trimAndLowercase` - 去除空白并转换为小写
- `addContext` - 添加上下文前缀

#### 使用方法
```typescript
import { useChat } from '@/hooks/useChat'
import { MESSAGE_PROCESSORS } from '@/utils/messageProcessors'

const chat = useChat({
  messagePreprocessor: MESSAGE_PROCESSORS.preprocessors.addContext
})
```

#### 自定义预处理器
```typescript
const customPreprocessor = (message: string): string => {
  return `[${new Date().toISOString()}] ${message}`
}

const chat = useChat({
  messagePreprocessor: customPreprocessor
})
```

### 3. 响应后处理功能

LLM 返回的内容会经过后处理函数处理，默认会检测 JSON 并提取 `output` 字段。

#### 内置后处理器
- `default` - 检测 JSON 并提取 `output` 字段，同时 console.log 完整 JSON 内容
- `removeMarkdown` - 移除 Markdown 格式

#### 默认后处理器行为
1. 检测响应中的 JSON 内容
2. 解析 JSON 并在控制台输出完整内容
3. 如果存在 `output` 字段，返回该字段的值
4. 否则返回原始响应

#### 使用方法
```typescript
import { useChat } from '@/hooks/useChat'
import { MESSAGE_PROCESSORS } from '@/utils/messageProcessors'

const chat = useChat({
  responsePostprocessor: MESSAGE_PROCESSORS.postprocessors.removeMarkdown
})
```

#### 自定义后处理器
```typescript
const customPostprocessor = (response: string): string => {
  try {
    const jsonMatch = response.match(/\{[\s\S]*\}/)
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[0])
      console.log('Found JSON:', parsed)
      return parsed.result || parsed.answer || response
    }
    return response
  } catch {
    return response
  }
}

const chat = useChat({
  responsePostprocessor: customPostprocessor
})
```

## 配置化聊天组件

新增了 `ChatWithConfig` 组件，提供可视化的配置界面。

### 功能特性
- **模型选择** - 支持选择不同的 AI 模型
- 系统提示选择
- 预处理器选择
- 后处理器选择
- 实时配置切换
- 聊天重置功能

### 支持的模型
- `bedrock-claude-4-sonnet` - Claude 4 Sonnet 模型（默认）
- `gpt-5` - GPT-5 模型

### 使用方法
```typescript
import { ChatWithConfig } from '@/components/chat/ChatWithConfig'

export default function Page() {
  return <ChatWithConfig />
}
```

## API 更新

### Chat API 端点更新
- 新增 `systemPrompt` 参数支持
- 自动将系统提示添加到消息列表开头

### 请求格式
```typescript
{
  messages: ChatMessage[],
  model?: string,
  temperature?: number,
  max_tokens?: number,
  systemPrompt?: string  // 新增
}
```

## 类型定义更新

### ChatRequest 接口
```typescript
export interface ChatRequest {
  messages: Omit<ChatMessage, "id" | "timestamp">[]
  model?: string
  temperature?: number
  max_tokens?: number
  systemPrompt?: string  // 新增
}
```

### 新增类型
```typescript
export type MessagePreprocessor = (message: string) => string
export type ResponsePostprocessor = (response: string) => string
export type SystemPromptKey = 'default' | 'creative' | 'technical' | 'analytical'
```

## 示例用法

查看 `src/examples/chatUsage.ts` 文件获取完整的使用示例。

## 测试

运行测试以验证消息处理器功能：
```bash
npm test src/utils/__tests__/messageProcessors.test.ts
```

## 扩展功能

### 添加新的系统提示
在 `src/config/prompts.ts` 中添加新的提示：
```typescript
export const SYSTEM_PROMPTS = {
  // ... 现有提示
  myCustomPrompt: `Your custom system prompt here`,
} as const
```

### 添加新的处理器
在 `src/utils/messageProcessors.ts` 中添加新的处理器：
```typescript
export const myCustomPreprocessor: MessagePreprocessor = (message: string): string => {
  // 自定义处理逻辑
  return processedMessage
}

// 添加到注册表
export const MESSAGE_PROCESSORS = {
  preprocessors: {
    // ... 现有处理器
    myCustom: myCustomPreprocessor,
  },
  // ...
}
```
