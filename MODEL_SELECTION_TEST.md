# 模型选择功能测试指南

## 功能概述

新增的模型选择功能允许用户在聊天界面中动态选择不同的 AI 模型。

## 支持的模型

- `bedrock-claude-4-sonnet` - Claude 4 Sonnet 模型（默认选择）
- `gpt-5` - GPT-5 模型

## 测试步骤

### 1. 启动应用
```bash
pnpm dev
```

### 2. 访问应用
打开浏览器访问 `http://localhost:3000`

### 3. 打开配置面板
点击右上角的 "Show Config" 按钮

### 4. 测试模型选择
1. 在配置面板中找到 "Model" 选择器
2. 默认应该选中 "bedrock-claude-4-sonnet"
3. 点击下拉菜单，应该看到两个选项：
   - bedrock-claude-4-sonnet
   - gpt-5
4. 选择不同的模型

### 5. 验证模型切换
1. 选择一个模型（如 gpt-5）
2. 发送一条测试消息
3. 检查网络请求（开发者工具 -> Network 标签）
4. 查看发送到 `/api/chat` 的请求体，确认 `model` 字段为选择的模型

### 6. 测试配置持久性
1. 选择一个模型
2. 发送几条消息
3. 切换到另一个模型
4. 再发送消息
5. 确认每次请求都使用了正确的模型

## 预期行为

### UI 行为
- 模型选择器应该显示在配置面板的顶部
- 下拉菜单应该包含两个模型选项
- 选择应该立即生效，无需重新加载页面
- 当前选择的模型应该在选择器中正确显示

### API 行为
- 发送到 `/api/chat` 的请求应该包含正确的 `model` 参数
- 不同模型的请求应该被正确处理
- 模型参数应该传递给底层的 OpenAI 客户端

### 错误处理
- 如果选择了不支持的模型，应该有适当的错误处理
- 网络错误应该被正确捕获和显示

## 代码验证

### 检查组件状态
在浏览器开发者工具中，可以通过 React DevTools 检查 `ChatWithConfig` 组件的状态：
- `selectedModel` 状态应该反映当前选择的模型
- 状态变化应该触发 `useChat` hook 的重新渲染

### 检查网络请求
在开发者工具的 Network 标签中：
1. 发送消息时应该看到对 `/api/chat` 的 POST 请求
2. 请求体应该包含：
   ```json
   {
     "messages": [...],
     "model": "选择的模型名称",
     "temperature": 0.7,
     "max_tokens": 2048,
     "systemPrompt": "..."
   }
   ```

## 故障排除

### 常见问题

1. **模型选择器不显示**
   - 确认配置面板已打开
   - 检查控制台是否有 JavaScript 错误

2. **选择不生效**
   - 检查 `selectedModel` 状态是否正确更新
   - 确认 `useChat` hook 接收到了正确的模型参数

3. **API 请求失败**
   - 检查所选模型是否在后端支持
   - 确认 API 密钥配置正确

### 调试技巧

1. **添加控制台日志**
   ```typescript
   console.log('Selected model:', selectedModel)
   ```

2. **检查 useChat 参数**
   ```typescript
   console.log('useChat options:', {
     model: selectedModel,
     systemPromptKey,
     // ...
   })
   ```

3. **监控状态变化**
   使用 React DevTools 监控组件状态的变化

## 扩展测试

### 添加新模型
如果需要添加新模型，修改 `AVAILABLE_MODELS` 数组：
```typescript
const AVAILABLE_MODELS = [
  "bedrock-claude-4-sonnet", 
  "gpt-5",
  "new-model-name"  // 新增模型
] as const
```

### 自动化测试
可以编写自动化测试来验证模型选择功能：
```typescript
// 示例测试用例
test('should change model when selection changes', () => {
  // 渲染组件
  // 选择不同模型
  // 验证状态更新
  // 验证 API 调用参数
})
```

## 成功标准

✅ 模型选择器正确显示两个选项
✅ 选择不同模型时状态正确更新
✅ API 请求包含正确的模型参数
✅ 模型切换不影响其他配置选项
✅ 界面响应流畅，无明显延迟
✅ 错误情况得到适当处理
