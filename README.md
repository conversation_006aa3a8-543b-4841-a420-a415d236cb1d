# Chat Assistant

A ChatGPT-like interface built with Next.js 15 and powered by LiteLLM. This application provides a modern, responsive chat interface with streaming responses and real-time message updates.

## Features

- 🚀 **Modern UI**: Built with Next.js 15, React 19, and Tailwind CSS
- 💬 **Real-time Chat**: Streaming responses with typing indicators
- 🎨 **Beautiful Design**: Clean, responsive interface using shadcn/ui components
- ⚡ **Fast Performance**: Optimized with Turbopack and modern React features
- 🔧 **LiteLLM Integration**: Compatible with multiple AI providers through LiteLLM
- 📱 **Mobile Friendly**: Responsive design that works on all devices
- 🛡️ **Error Handling**: Comprehensive error boundaries and user feedback
- ♿ **Accessibility**: Built with accessibility best practices

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **UI**: React 19, Tailwind CSS, shadcn/ui
- **AI Integration**: OpenAI SDK with LiteLLM
- **Database**: MongoDB with native driver
- **TypeScript**: Full type safety
- **Deployment**: Cloudflare Pages (via OpenNext)

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm/yarn
- A running LiteLLM server

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd memorai
```

2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
# LiteLLM Configuration
baseUrl=http://localhost:4000/v1
token=your-api-token-here

# MongoDB Configuration
mongoUrl=mongodb://localhost:27017/copilot
```

4. Start the development server:
```bash
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Configuration

### Environment Variables

- `baseUrl`: The base URL of your LiteLLM server (e.g., `http://localhost:4000/v1`)
- `token`: Your API token for authentication

### LiteLLM Setup

This application requires a running LiteLLM server. LiteLLM provides a unified interface for multiple AI providers. For setup instructions, visit the [LiteLLM documentation](https://docs.litellm.ai/).

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/chat/          # Chat API endpoint
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── chat/             # Chat-related components
│   └── ErrorBoundary.tsx # Error handling
├── hooks/                # Custom React hooks
│   └── useChat.ts        # Chat logic hook
├── types/                # TypeScript definitions
│   └── chat.ts           # Chat-related types
└── ui/                   # UI components (shadcn/ui)
```

## API

### POST /api/chat

Sends a message to the AI and returns a streaming response.

**Request Body:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "model": "",
  "temperature": 0.7,
  "max_tokens": 2048
}
```

**Response:**
Server-Sent Events stream with JSON chunks containing the AI response.

## Deployment

### Cloudflare Pages

This project is configured for deployment on Cloudflare Pages using OpenNext:

```bash
pnpm run deploy
```

### Other Platforms

The application can be deployed on any platform that supports Next.js:

- Vercel: `vercel deploy`
- Netlify: Build command `pnpm build`, publish directory `.next`
- Docker: Use the included Dockerfile (if present)

## MongoDB Features

This project includes comprehensive MongoDB integration with three main read operations:

### 1. Get User Queries
- **Collection**: `copilot.gem_task_response`
- **Function**: Retrieves query history for a specific user
- **Filtering**: Automatically filters out invalid queries like "Style with this"

### 2. Get User Document IDs
- **Collection**: `copilot.user_saved_collections`
- **Function**: Retrieves saved product document IDs for a specific user
- **Filtering**: Only returns documents where `collection_type` is "product"

### 3. Get Product Information
- **Collection**: `copilot.deal_info`
- **Function**: Retrieves detailed product information by ID list
- **Format**: IDs should be in format `"{product_id}-{host}"`
- **Returns**: host, product_id, brand, title, categories fields

### Usage Examples

```typescript
import {
  getQueriesByUserId,
  getDocumentIdsByUserId,
  getProductInfoByIds
} from '@/utils/mongodbClient'

// Get user queries
const queries = await getQueriesByUserId('user123')

// Get user document IDs
const documentIds = await getDocumentIdsByUserId('user123')

// Get product information
const products = await getProductInfoByIds(['12345-amazon.com'])
```

For detailed documentation, see [MONGODB_FEATURES.md](./MONGODB_FEATURES.md).

### Testing MongoDB Features

```bash
# Run the test script
node test-mongodb-features.js

# Or run unit tests
npm test src/utils/__tests__/mongodb.test.ts
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
