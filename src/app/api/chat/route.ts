import { NextRequest, NextResponse } from "next/server"
import { OpenAI } from "openai"

// 初始化 OpenAI 客户端，使用 LiteLLM 服务
const openai = new OpenAI({
  baseURL: process.env.LITELLM_BASE_URL,
  apiKey: process.env.LITELLM_API_KEY,
})

export interface ChatMessage {
  role: "user" | "assistant" | "system"
  content: string
}

export interface ChatRequest {
  messages: ChatMessage[]
  model?: string
  temperature?: number
  max_tokens?: number
  systemPrompt?: string
}

export async function POST(request: NextRequest) {
  try {
    const body: ChatRequest = await request.json()
    const {
      messages,
      model = "bedrock-claude-4-sonnet",
      temperature = 0.7,
      max_tokens = 2048,
      systemPrompt,
    } = body

    // 验证请求数据
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: "Messages array is required and cannot be empty" },
        { status: 400 },
      )
    }

    // 验证消息格式
    for (const message of messages) {
      if (!message.role || !message.content) {
        return NextResponse.json(
          { error: "Each message must have role and content" },
          { status: 400 },
        )
      }
      if (!["user", "assistant", "system"].includes(message.role)) {
        return NextResponse.json(
          { error: "Message role must be user, assistant, or system" },
          { status: 400 },
        )
      }
    }

    // 准备消息数组，如果有系统提示则添加到开头
    const finalMessages: ChatMessage[] = systemPrompt
      ? [{ role: "system", content: systemPrompt }, ...messages]
      : messages

    // 创建非流式响应
    const completion = await openai.chat.completions.create({
      model,
      messages: finalMessages,
      temperature,
      max_tokens,
      stream: false,
    })

    const content = completion.choices[0]?.message?.content || ""

    // 返回 JSON 响应
    return NextResponse.json({
      content,
      usage: completion.usage,
    })
  } catch (error) {
    console.error("Chat API error:", error)

    // 处理不同类型的错误
    if (error instanceof Error) {
      if (error.message.includes("API key")) {
        return NextResponse.json(
          { error: "Invalid API configuration" },
          { status: 401 },
        )
      }
      if (error.message.includes("rate limit")) {
        return NextResponse.json(
          { error: "Rate limit exceeded" },
          { status: 429 },
        )
      }
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    )
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  })
}
