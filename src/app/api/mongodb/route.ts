import { NextRequest, NextResponse } from "next/server"

import { MongoDBRequest, MongoDBResponse, ProductInfo } from "@/types/mongodb"
import {
  getDocumentIdsByUserId,
  getProductInfoByIds,
  getQueriesByUserId,
} from "@/utils/mongodb"

export async function POST(request: NextRequest) {
  try {
    const body: MongoDBRequest = await request.json()
    const { action, userId, ids, useCache = true } = body

    // 验证请求数据
    if (!action) {
      return NextResponse.json(
        { success: false, error: "Action is required" },
        { status: 400 },
      )
    }

    let result: string[] | ProductInfo[]

    switch (action) {
      case "getQueries":
        if (!userId) {
          return NextResponse.json(
            {
              success: false,
              error: "userId is required for getQueries action",
            },
            { status: 400 },
          )
        }
        result = await getQueriesByUserId(userId, useCache)
        break

      case "getDocumentIds":
        if (!userId) {
          return NextResponse.json(
            {
              success: false,
              error: "userId is required for getDocumentIds action",
            },
            { status: 400 },
          )
        }
        result = await getDocumentIdsByUserId(userId, useCache)
        break

      case "getProductInfo":
        if (!ids || !Array.isArray(ids) || ids.length === 0) {
          return NextResponse.json(
            {
              success: false,
              error: "ids array is required for getProductInfo action",
            },
            { status: 400 },
          )
        }
        result = await getProductInfoByIds(ids, useCache)
        break

      default:
        return NextResponse.json(
          { success: false, error: "Invalid action" },
          { status: 400 },
        )
    }

    return NextResponse.json({
      success: true,
      data: result,
    })
  } catch (error) {
    console.error("MongoDB API error:", error)

    // 处理不同类型的错误
    if (error instanceof Error) {
      if (error.message.includes("MongoDB URL not found")) {
        return NextResponse.json(
          { success: false, error: "Database configuration error" },
          { status: 500 },
        )
      }
      if (error.message.includes("connection")) {
        return NextResponse.json(
          { success: false, error: "Database connection error" },
          { status: 503 },
        )
      }
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 },
    )
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  })
}
