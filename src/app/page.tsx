"use client"

import { useState } from "react"

import { ChatWithConfig } from "@/components/chat/ChatWithConfig"
import { MongoDBDemo } from "@/components/MongoDBDemo"

export default function Home() {
  const [activeTab, setActiveTab] = useState<"chat" | "mongodb">("chat")

  return (
    <div className="flex h-screen w-full flex-col">
      {/* 标签导航 */}
      {/* <div className="border-b border-gray-200 bg-white">
        <div className="flex space-x-8 px-6">
          <button
            onClick={() => setActiveTab("chat")}
            className={`border-b-2 px-1 py-4 text-sm font-medium ${
              activeTab === "chat"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
            }`}
          >
            AI 聊天
          </button>
          <button
            onClick={() => setActiveTab("mongodb")}
            className={`border-b-2 px-1 py-4 text-sm font-medium ${
              activeTab === "mongodb"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
            }`}
          >
            MongoDB 演示
          </button>
        </div>
      </div> */}

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        {activeTab === "chat" && (
          <div className="h-full">
            <ChatWithConfig />
          </div>
        )}
        {activeTab === "mongodb" && (
          <div className="h-full overflow-auto bg-gray-50">
            <MongoDBDemo />
          </div>
        )}
      </div>
    </div>
  )
}
