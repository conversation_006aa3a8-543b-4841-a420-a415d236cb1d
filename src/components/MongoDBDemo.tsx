"use client"

import { useState } from "react"

import { ProductInfo } from "@/types/mongodb"
import {
  getDocumentIdsByUserId,
  getProductInfoByIds,
  getQueriesByUserId,
} from "@/utils/mongodbClient"

export function MongoDBDemo() {
  const [userId, setUserId] = useState("7269565388859998208")
  const [productIds, setProductIds] = useState(
    "********-nordstrom.com,B001GK4SG0-amazon.com",
  )
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<{
    queries?: string[]
    documentIds?: string[]
    productInfos?: ProductInfo[]
    error?: string
  }>({})

  const handleGetQueries = async () => {
    setLoading(true)
    try {
      const queries = await getQueriesByUserId(userId)
      setResults((prev) => ({ ...prev, queries, error: undefined }))
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "获取查询失败",
      }))
    } finally {
      setLoading(false)
    }
  }

  const handleGetDocumentIds = async () => {
    setLoading(true)
    try {
      const documentIds = await getDocumentIdsByUserId(userId)
      setResults((prev) => ({ ...prev, documentIds, error: undefined }))
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "获取文档ID失败",
      }))
    } finally {
      setLoading(false)
    }
  }

  const handleGetProductInfo = async () => {
    setLoading(true)
    try {
      const ids = productIds
        .split(",")
        .map((id) => id.trim())
        .filter((id) => id)
      const productInfos = await getProductInfoByIds(ids)
      setResults((prev) => ({ ...prev, productInfos, error: undefined }))
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "获取产品信息失败",
      }))
    } finally {
      setLoading(false)
    }
  }

  const clearResults = () => {
    setResults({})
  }

  return (
    <div className="mx-auto max-w-4xl space-y-6 p-6">
      <div className="rounded-lg bg-white p-6 shadow-md">
        <h2 className="mb-4 text-2xl font-bold text-gray-800">
          MongoDB 功能演示
        </h2>
        <p className="mb-6 text-gray-600">
          演示三个主要的MongoDB读取功能：获取用户查询历史、获取用户文档ID、获取产品信息
        </p>

        {/* 用户ID输入 */}
        <div className="mb-4">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            用户ID
          </label>
          <input
            type="text"
            value={userId}
            onChange={(e) => setUserId(e.target.value)}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            placeholder="输入用户ID"
          />
        </div>

        {/* 产品ID输入 */}
        <div className="mb-6">
          <label className="mb-2 block text-sm font-medium text-gray-700">
            产品ID列表 (格式: product_id-host, 用逗号分隔)
          </label>
          <input
            type="text"
            value={productIds}
            onChange={(e) => setProductIds(e.target.value)}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            placeholder="例如: 12345-amazon.com,67890-ebay.com"
          />
        </div>

        {/* 操作按钮 */}
        <div className="mb-6 flex flex-wrap gap-3">
          <button
            onClick={handleGetQueries}
            disabled={loading || !userId}
            className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:cursor-not-allowed disabled:bg-gray-400"
          >
            {loading ? "加载中..." : "获取查询历史"}
          </button>
          <button
            onClick={handleGetDocumentIds}
            disabled={loading || !userId}
            className="rounded-md bg-green-500 px-4 py-2 text-white hover:bg-green-600 disabled:cursor-not-allowed disabled:bg-gray-400"
          >
            {loading ? "加载中..." : "获取文档ID"}
          </button>
          <button
            onClick={handleGetProductInfo}
            disabled={loading || !productIds}
            className="rounded-md bg-purple-500 px-4 py-2 text-white hover:bg-purple-600 disabled:cursor-not-allowed disabled:bg-gray-400"
          >
            {loading ? "加载中..." : "获取产品信息"}
          </button>
          <button
            onClick={clearResults}
            className="rounded-md bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
          >
            清除结果
          </button>
        </div>

        {/* 错误显示 */}
        {results.error && (
          <div className="mb-4 rounded-md border border-red-400 bg-red-100 p-4 text-red-700">
            <strong>错误:</strong> {results.error}
          </div>
        )}

        {/* 结果显示 */}
        <div className="space-y-4">
          {/* 查询历史结果 */}
          {results.queries && (
            <div className="rounded-md bg-blue-50 p-4">
              <h3 className="mb-2 text-lg font-semibold text-blue-800">
                查询历史 ({results.queries.length} 条)
              </h3>
              {results.queries.length > 0 ? (
                <ul className="list-inside list-disc space-y-1">
                  {results.queries.map((query, index) => (
                    <li key={index} className="text-blue-700">
                      {query}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-blue-600">没有找到查询历史</p>
              )}
            </div>
          )}

          {/* 文档ID结果 */}
          {results.documentIds && (
            <div className="rounded-md bg-green-50 p-4">
              <h3 className="mb-2 text-lg font-semibold text-green-800">
                文档ID ({results.documentIds.length} 个)
              </h3>
              {results.documentIds.length > 0 ? (
                <ul className="list-inside list-disc space-y-1">
                  {results.documentIds.map((id, index) => (
                    <li
                      key={index}
                      className="font-mono text-sm text-green-700"
                    >
                      {id}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-green-600">没有找到文档ID</p>
              )}
            </div>
          )}

          {/* 产品信息结果 */}
          {results.productInfos && (
            <div className="rounded-md bg-purple-50 p-4">
              <h3 className="mb-2 text-lg font-semibold text-purple-800">
                产品信息 ({results.productInfos.length} 个)
              </h3>
              {results.productInfos.length > 0 ? (
                <div className="space-y-3">
                  {results.productInfos.map((product, index) => (
                    <div key={index} className="rounded border bg-white p-3">
                      <h4 className="font-semibold text-gray-800">
                        {product.title}
                      </h4>
                      <p className="text-sm text-gray-600">
                        品牌: {product.brand}
                      </p>
                      <p className="text-sm text-gray-600">
                        主机: {product.host}
                      </p>
                      <p className="text-sm text-gray-600">
                        产品ID: {product.product_id}
                      </p>
                      <p className="text-sm text-gray-600">
                        分类: {product.categories?.join(", ") || "无"}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-purple-600">没有找到产品信息</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
