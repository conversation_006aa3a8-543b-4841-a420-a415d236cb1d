"use client"

import { Send, Square } from "lucide-react"
import { KeyboardEvent, useRef, useState } from "react"

import { But<PERSON> } from "@/ui/components/ui/button"
import { Textarea } from "@/ui/components/ui/textarea"
import { cn } from "@/ui/lib/utils"

interface ChatInputProps {
  onSendMessage: (message: string) => void
  onStopGeneration?: () => void
  isLoading?: boolean
  disabled?: boolean
  placeholder?: string
  className?: string
}

export function ChatInput({
  onSendMessage,
  onStopGeneration,
  isLoading = false,
  disabled = false,
  placeholder = "Type your message...",
  className,
}: ChatInputProps) {
  const [message, setMessage] = useState("")
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const handleSubmit = () => {
    const trimmedMessage = message.trim()
    if (!trimmedMessage || disabled || isLoading) return

    onSendMessage(trimmedMessage)
    setMessage("")

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
    }
  }

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleStop = () => {
    onStopGeneration?.()
  }

  // Auto-resize textarea
  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = e.target
    setMessage(textarea.value)

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = "auto"
    // Set height to scrollHeight, with max height limit
    textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`
  }

  return (
    <div className={cn("bg-background border-t p-4", className)}>
      <div className="mx-auto max-w-4xl">
        <div className="flex items-end gap-2">
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={handleInput}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className="bg-muted/50 min-h-[44px] resize-none border-0 p-3 shadow-none focus-visible:ring-1"
              style={{ height: "auto" }}
            />
          </div>

          <Button
            onClick={isLoading ? handleStop : handleSubmit}
            size="icon"
            variant={isLoading ? "outline" : "default"}
            className="h-11 w-11 shrink-0"
            disabled={
              disabled ||
              (!isLoading && !message.trim()) ||
              (isLoading && !onStopGeneration)
            }
          >
            {isLoading ? (
              <Square className="h-4 w-4" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            <span className="sr-only">
              {isLoading ? "Stop generation" : "Send message"}
            </span>
          </Button>
        </div>

        <div className="text-muted-foreground mt-2 text-center text-xs">
          Press Enter to send, Shift + Enter for new line
        </div>
      </div>
    </div>
  )
}
