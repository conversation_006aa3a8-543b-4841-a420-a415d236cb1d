"use client"

import { <PERSON><PERSON>, Use<PERSON> } from "lucide-react"

import { ChatMessage as ChatMessageType } from "@/types/chat"
import { cn } from "@/ui/lib/utils"

interface ChatMessageProps {
  message: ChatMessageType
  className?: string
}

export function ChatMessage({ message, className }: ChatMessageProps) {
  const isUser = message.role === "user"
  const isAssistant = message.role === "assistant"

  return (
    <div
      className={cn(
        "flex w-full gap-3 px-4 py-6",
        isUser ? "bg-background" : "bg-muted/50",
        className,
      )}
    >
      {/* Avatar */}
      <div
        className={cn(
          "flex h-8 w-8 shrink-0 items-center justify-center rounded-md border shadow select-none",
          isUser ? "bg-background" : "bg-primary text-primary-foreground",
        )}
      >
        {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
      </div>

      {/* Message Content */}
      <div className="flex-1 space-y-2 overflow-hidden">
        <div className="flex items-center gap-2">
          <span className="text-sm font-semibold">
            {isUser ? "You" : "Assistant"}
          </span>
          <span className="text-muted-foreground text-xs">
            {message.timestamp.toLocaleTimeString()}
          </span>
        </div>

        <div className="prose prose-sm dark:prose-invert max-w-none">
          <div className="break-words whitespace-pre-wrap">
            {message.content}
          </div>
        </div>
      </div>
    </div>
  )
}
