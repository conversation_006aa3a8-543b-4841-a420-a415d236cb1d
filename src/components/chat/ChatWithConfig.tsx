"use client"

import { useState } from "react"

import { getSystemPromptKeys, SystemPromptKey } from "@/config/prompts"
import { useChat } from "@/hooks/useChat"
import { Button } from "@/ui/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/ui/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/components/ui/select"
import { cn } from "@/ui/lib/utils"
import {
  MESSAGE_PROCESSORS,
  PostprocessorKey,
  PreprocessorKey,
} from "@/utils/messageProcessors"

import { Chat } from "./Chat"

// 可选的模型列表
const AVAILABLE_MODELS = ["bedrock-claude-4-sonnet", "gpt-5"] as const
type ModelType = (typeof AVAILABLE_MODELS)[number]

interface ChatWithConfigProps {
  className?: string
}

export function ChatWithConfig({ className }: ChatWithConfigProps) {
  const [systemPromptKey, setSystemPromptKey] =
    useState<SystemPromptKey>("default")
  const [preprocessorKey, setPreprocessorKey] =
    useState<PreprocessorKey>("default")
  const [postprocessorKey, setPostprocessorKey] =
    useState<PostprocessorKey>("default")
  const [selectedModel, setSelectedModel] = useState<ModelType>(
    "bedrock-claude-4-sonnet",
  )
  const [showConfig, setShowConfig] = useState(false)

  const { messages, isLoading, error, sendMessage, stopGeneration, reset } =
    useChat({
      systemPromptKey,
      messagePreprocessor: MESSAGE_PROCESSORS.preprocessors[preprocessorKey],
      responsePostprocessor:
        MESSAGE_PROCESSORS.postprocessors[postprocessorKey],
      model: selectedModel,
    })

  return (
    <div className={cn("flex h-full", className)}>
      {/* Configuration Panel */}
      {showConfig && (
        <Card className="m-4 h-fit w-80">
          <CardHeader>
            <CardTitle>Chat Configuration</CardTitle>
            <CardDescription>
              Configure system prompts and message processors
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Model Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Model</label>
              <Select
                value={selectedModel}
                onValueChange={(value: ModelType) => setSelectedModel(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {AVAILABLE_MODELS.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* System Prompt Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">System Prompt</label>
              <Select
                value={systemPromptKey}
                onValueChange={(value: SystemPromptKey) =>
                  setSystemPromptKey(value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {getSystemPromptKeys().map((key) => (
                    <SelectItem key={key} value={key}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Message Preprocessor Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Input Preprocessor</label>
              <Select
                value={preprocessorKey}
                onValueChange={(value: PreprocessorKey) =>
                  setPreprocessorKey(value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.keys(MESSAGE_PROCESSORS.preprocessors).map((key) => (
                    <SelectItem key={key} value={key}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Response Postprocessor Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Response Postprocessor
              </label>
              <Select
                value={postprocessorKey}
                onValueChange={(value: PostprocessorKey) =>
                  setPostprocessorKey(value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.keys(MESSAGE_PROCESSORS.postprocessors).map((key) => (
                    <SelectItem key={key} value={key}>
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Reset Button */}
            <Button onClick={reset} variant="outline" className="w-full">
              Reset Chat
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Chat Interface */}
      <div className="relative flex-1">
        {/* Config Toggle Button */}
        <Button
          onClick={() => setShowConfig(!showConfig)}
          variant="outline"
          size="sm"
          className="absolute top-4 right-4 z-10"
        >
          {showConfig ? "Hide Config" : "Show Config"}
        </Button>

        <Chat
          messages={messages}
          isLoading={isLoading}
          error={error}
          onSendMessage={sendMessage}
          onStopGeneration={stopGeneration}
        />
      </div>
    </div>
  )
}
