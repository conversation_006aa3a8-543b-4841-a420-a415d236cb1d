"use client"

import { useEffect, useRef } from "react"

import { ChatMessage as ChatMessageType } from "@/types/chat"
import { cn } from "@/ui/lib/utils"

import { ChatMessage } from "./ChatMessage"

interface MessageListProps {
  messages: ChatMessageType[]
  isLoading?: boolean
  className?: string
}

export function MessageList({
  messages,
  isLoading,
  className,
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  if (messages.length === 0) {
    return (
      <div className={cn("flex flex-1 items-center justify-center", className)}>
        <div className="text-center">
          <div className="text-muted-foreground mb-2 text-2xl font-semibold">
            Welcome to Chat
          </div>
          <div className="text-muted-foreground">
            Start a conversation by typing a message below.
          </div>
          {isLoading && (
            <div className="flex h-8 items-center justify-center pt-6">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-600 border-t-transparent"></div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={cn("flex-1 overflow-y-auto", className)}>
      <div className="flex flex-col">
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}

        {/* Loading indicator */}
        {isLoading && (
          <div className="bg-muted/50 flex w-full gap-3 px-4 py-6">
            <div className="bg-primary text-primary-foreground flex h-8 w-8 shrink-0 items-center justify-center rounded-md border shadow select-none">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            </div>
            <div className="flex-1 space-y-2 overflow-hidden">
              <div className="flex items-center gap-2">
                <span className="text-sm font-semibold">Assistant</span>
              </div>
              <div className="text-muted-foreground text-sm">Thinking...</div>
            </div>
          </div>
        )}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}
