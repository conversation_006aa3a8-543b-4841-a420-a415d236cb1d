"use client"

import { useCallback, useRef, useState } from "react"

import { getSystemPrompt, SystemPromptKey } from "@/config/prompts"
import {
  ChatMessage,
  ChatState,
  DEFAULT_CHAT_CONFIG,
  MessagePreprocessor,
  ResponsePostprocessor,
} from "@/types/chat"
import {
  defaultMessagePreprocessor,
  defaultResponsePostprocessor,
} from "@/utils/messageProcessors"

interface UseChatOptions {
  initialMessages?: ChatMessage[]
  model?: string
  temperature?: number
  max_tokens?: number
  systemPromptKey?: SystemPromptKey
  messagePreprocessor?: MessagePreprocessor
  responsePostprocessor?: ResponsePostprocessor
}

export function useChat(options: UseChatOptions = {}) {
  const {
    initialMessages = [],
    model = DEFAULT_CHAT_CONFIG.model,
    temperature = DEFAULT_CHAT_CONFIG.temperature,
    max_tokens = DEFAULT_CHAT_CONFIG.max_tokens,
    systemPromptKey = "default",
    messagePreprocessor = defaultMessagePreprocessor,
    responsePostprocessor = defaultResponsePostprocessor,
  } = options

  const [state, setState] = useState<ChatState>({
    messages: initialMessages,
    isLoading: false,
    error: null,
  })

  const abortControllerRef = useRef<AbortController | null>(null)

  // 生成唯一 ID
  const generateId = () =>
    `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

  // 添加消息
  const addMessage = useCallback(
    (message: Omit<ChatMessage, "id" | "timestamp">) => {
      const newMessage: ChatMessage = {
        ...message,
        id: generateId(),
        timestamp: new Date(),
      }

      setState((prev) => ({
        ...prev,
        messages: [...prev.messages, newMessage],
      }))

      return newMessage
    },
    [],
  )

  // 更新消息内容
  const updateMessage = useCallback(
    (id: string, updates: Partial<ChatMessage>) => {
      setState((prev) => ({
        ...prev,
        messages: prev.messages.map((msg) =>
          msg.id === id ? { ...msg, ...updates } : msg,
        ),
      }))
    },
    [],
  )

  // 发送消息
  const sendMessage = useCallback(
    async (content: string) => {
      if (state.isLoading) return

      // 设置加载状态
      setState((prev) => ({
        ...prev,
        isLoading: true,
        error: null,
      }))

      // 添加用户消息（使用原始内容显示）
      const userMessage = addMessage({
        role: "user",
        content,
      })

      // 预处理用户输入
      const processedContent = await messagePreprocessor(content)

      try {
        // 创建 AbortController 用于取消请求
        const abortController = new AbortController()
        abortControllerRef.current = abortController

        // 准备请求数据
        const messages = [...state.messages, userMessage].map((msg, index) => {
          // 对最后一条消息（用户输入）使用预处理后的内容
          if (index === state.messages.length) {
            return {
              role: msg.role,
              content: processedContent,
            }
          }
          return {
            role: msg.role,
            content: msg.content,
          }
        })

        // 获取系统提示
        const systemPrompt = getSystemPrompt(systemPromptKey)

        const response = await fetch("/api/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            messages,
            model,
            temperature,
            max_tokens,
            systemPrompt,
          }),
          signal: abortController.signal,
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(
            (errorData as { error: string }).error || `HTTP ${response.status}`,
          )
        }

        // 处理 JSON 响应
        const data: { content: string } = await response.json()

        if (data.content) {
          // 后处理响应内容
          const processedResponse = await responsePostprocessor(data.content)

          // 添加助手消息
          addMessage({
            role: "assistant",
            content: processedResponse,
          })
        } else {
          throw new Error("No content in response")
        }
      } catch (error) {
        console.error("Chat error:", error)

        setState((prev) => ({
          ...prev,
          error: error instanceof Error ? error.message : "An error occurred",
        }))
      } finally {
        setState((prev) => ({
          ...prev,
          isLoading: false,
        }))
        abortControllerRef.current = null
      }
    },
    [
      state.messages,
      state.isLoading,
      addMessage,
      model,
      temperature,
      max_tokens,
      systemPromptKey,
      messagePreprocessor,
      responsePostprocessor,
    ],
  )

  // 停止生成
  const stopGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }

    setState((prev) => ({
      ...prev,
      isLoading: false,
    }))
  }, [])

  // 清除错误
  const clearError = useCallback(() => {
    setState((prev) => ({
      ...prev,
      error: null,
    }))
  }, [])

  // 重置聊天
  const reset = useCallback(() => {
    stopGeneration()
    setState({
      messages: initialMessages,
      isLoading: false,
      error: null,
    })
  }, [initialMessages, stopGeneration])

  return {
    messages: state.messages,
    isLoading: state.isLoading,
    error: state.error,
    sendMessage,
    stopGeneration,
    clearError,
    reset,
  }
}
