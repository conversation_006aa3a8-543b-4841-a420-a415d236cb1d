/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * MongoDB相关类型定义
 */

/**
 * 产品信息接口
 */
export interface ProductInfo {
  /** 主机名 */
  host?: string
  /** 产品ID */
  product_id?: string
  /** 品牌 */
  brand?: string
  /** 标题 */
  title?: string
  /** 分类数组 */
  categories?: string[]
}

/**
 * MongoDB API请求接口
 */
export interface MongoDBRequest {
  /** 操作类型 */
  action: "getQueries" | "getDocumentIds" | "getProductInfo"
  /** 用户ID（getQueries和getDocumentIds操作需要） */
  userId?: string
  /** ID列表（getProductInfo操作需要） */
  ids?: string[]
  /** 是否使用缓存，默认为true */
  useCache?: boolean
}

/**
 * MongoDB API响应接口
 */
export interface MongoDBResponse<T = string[] | ProductInfo[]> {
  /** 操作是否成功 */
  success: boolean
  /** 返回的数据 */
  data?: T
  /** 错误信息 */
  error?: string
}

/**
 * 查询响应类型
 */
export type QueriesResponse = MongoDBResponse<string[]>

/**
 * 文档ID响应类型
 */
export type DocumentIdsResponse = MongoDBResponse<string[]>

/**
 * 产品信息响应类型
 */
export type ProductInfoResponse = MongoDBResponse<ProductInfo[]>

/**
 * gem_task_response集合文档接口
 */
export interface GemTaskResponseDocument {
  _id?: any
  user_id: string
  query: string
  [key: string]: any
}

/**
 * user_saved_collections集合文档接口
 */
export interface UserSavedCollectionsDocument {
  _id?: any
  user_id: string
  document_id: string
  collection_type: string
  [key: string]: any
}

/**
 * deal_info集合文档接口
 */
export interface DealInfoDocument {
  _id?: any
  product_id: string
  host: string
  brand: string
  title: string
  categories: string[]
  [key: string]: any
}

/**
 * MongoDB客户端响应接口
 */
export interface MongoDBClientResponse<T> {
  success: boolean
  data?: T
  error?: string
}

/**
 * 无效查询列表类型
 */
export type InvalidQueries = readonly string[]

/**
 * 默认无效查询列表
 */
export const DEFAULT_INVALID_QUERIES: InvalidQueries = [
  "complete the look",
  "outfit styling for this piece",
  "find similar for this item",
  "style with it",
  "matching",
] as const

export function isValidQuery(q: string) {
  return (
    !DEFAULT_INVALID_QUERIES.includes(q.toLowerCase()) &&
    !q.toLowerCase().startsWith("find similar") &&
    !q.toLowerCase().startsWith("style with")
  )
}

/**
 * MongoDB操作类型
 */
export type MongoDBAction = MongoDBRequest["action"]

/**
 * ID格式验证结果
 */
export interface ParsedId {
  /** 是否有效 */
  isValid: boolean
  /** 产品ID */
  productId?: string
  /** 主机名 */
  host?: string
  /** 原始ID */
  originalId: string
}

/**
 * MongoDB连接配置
 */
export interface MongoDBConfig {
  /** MongoDB连接URL */
  url: string
  /** 数据库名称 */
  database: string
}

/**
 * 集合名称常量
 */
export const COLLECTIONS = {
  GEM_TASK_RESPONSE: "gem_task_response",
  USER_SAVED_COLLECTIONS: "user_saved_collections",
  DEAL_INFO: "deal_info",
  GEM_TASK_PRODUCT_LIST: "gem_task_product_list",
} as const

/**
 * 集合名称类型
 */
export type CollectionName = (typeof COLLECTIONS)[keyof typeof COLLECTIONS]
